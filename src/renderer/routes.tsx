import { lazy, Suspense } from 'react'
import { Route } from 'react-router-dom'

import { Router } from 'lib/electron-router-dom'
import { Layout } from './layout'
import { MinimalLoader } from './components/LoadingSpinner'
import { ProtectedRoute } from './components/ProtectedRoute'
import { AuthProvider } from './contexts/AuthContext'
import { NotificationProvider } from './contexts/NotificationContext'

// Lazy load screens for better performance and to trigger Suspense
const AboutScreen = lazy(() => import('./screens/about.screen').then(module => ({ default: module.AboutScreen })))
const MainScreen = lazy(() => import('./screens/main.screen').then(module => ({ default: module.MainScreen })))
const TodosScreen = lazy(() => import('./screens/todos.screen').then(module => ({ default: module.TodosScreen })))
const ExampleUsageScreen = lazy(() => import('./screens/example-usage.screen').then(module => ({ default: module.ExampleUsageScreen })))
const CsvUploadScreen = lazy(() => import('./screens/csv-upload.screen').then(module => ({ default: module.CsvUploadScreen })))
const CsvFolderMonitorScreen = lazy(() => import('./screens/csv-folder-monitor.screen').then(module => ({ default: module.CsvFolderMonitorScreen })))
const PostgreSQLTestScreen = lazy(() => import('./screens/postgresql-test.screen').then(module => ({ default: module.PostgreSQLTestScreen })))
const PCloudTestScreen = lazy(() => import('./screens/pcloud-test.screen').then(module => ({ default: module.PCloudTestScreen })))
const BankMasterScreen = lazy(() => import('./screens/bank-master.screen').then(module => ({ default: module.BankMasterScreen })))
const NetworkServiceScreen = lazy(() => import('./screens/network-service.screen').then(module => ({ default: module.NetworkServiceScreen })))
const LoginScreen = lazy(() => import('./screens/login.screen').then(module => ({ default: module.LoginScreen })))
const UserManagementScreen = lazy(() => import('./screens/user-management.screen').then(module => ({ default: module.UserManagementScreen })))
const ModalExamplesScreen = lazy(() => import('./screens/modal-examples.screen').then(module => ({ default: module.ModalExamples })))
const CompanySettingsScreen = lazy(() => import('./screens/company-settings.screen').then(module => ({ default: module.CompanySettingsScreen })))
const MerchantManagementScreen = lazy(() => import('./screens/merchant-management.screen').then(module => ({ default: module.MerchantManagementScreen })))

export function Routes() {
  return (
    <AuthProvider>
      <NotificationProvider>
        <Router
        main={
          <>
            {/* Login Route - Not Protected */}
            <Route
              path="/login"
              element={
                <Suspense fallback={<MinimalLoader />}>
                  <LoginScreen />
                </Suspense>
              }
            />

            {/* Protected Routes */}
            <Route path="/" element={<Layout />}>
              <Route
                path="/"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <MainScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/about"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <AboutScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/todos"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <TodosScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/example-usage"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <ExampleUsageScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/csv-upload"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <CsvUploadScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/csv-folder-monitor"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <CsvFolderMonitorScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/postgresql-test"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <PostgreSQLTestScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/pcloud-test"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <PCloudTestScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/bank-master"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <BankMasterScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/network-service"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <NetworkServiceScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/user-management"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<MinimalLoader />}>
                      <UserManagementScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/modal-examples"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <ModalExamplesScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/company-settings"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<MinimalLoader />}>
                      <CompanySettingsScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/merchant-management"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<MinimalLoader />}>
                      <MerchantManagementScreen />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
            </Route>
          </>
        }
      />
      </NotificationProvider>
    </AuthProvider>
  )
}
