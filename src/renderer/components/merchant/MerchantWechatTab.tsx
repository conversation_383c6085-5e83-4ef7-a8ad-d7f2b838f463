import React, { useState, useEffect } from "react";
import { Button } from "../button";

interface Merchant {
  merchant_id?: number;
  merchant_name: string;
}

interface MerchantWechat {
  merchant_wechat_id?: number;
  merchant_id: number;
  wechat_rate?: number;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface MerchantWechatTabProps {
  merchantWechat: MerchantWechat | null;
  onSave: (wechatData: Omit<MerchantWechat, "merchant_wechat_id" | "create_dt" | "update_dt">) => Promise<void>;
  editingMerchant: Merchant | null;
}

export function MerchantWechatTab({
  merchantWechat,
  onSave,
  editingMerchant
}: MerchantWechatTabProps) {
  const [formData, setFormData] = useState({
    wechat_rate: 0,
    active: true,
  });
  const [isSaving, setIsSaving] = useState(false);

  // Update form data when merchantWechat changes
  useEffect(() => {
    if (merchantWechat) {
      setFormData({
        wechat_rate: merchantWechat.wechat_rate || 0,
        active: merchantWechat.active,
      });
    } else {
      setFormData({
        wechat_rate: 0,
        active: true,
      });
    }
  }, [merchantWechat]);

  const handleSave = async () => {
    if (!editingMerchant?.merchant_id) {
      alert("Please save merchant details first");
      return;
    }

    setIsSaving(true);
    try {
      await onSave({
        merchant_id: editingMerchant.merchant_id,
        wechat_rate: formData.wechat_rate || undefined,
        active: formData.active,
        create_by: "SYSTEM",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (!editingMerchant?.merchant_id) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500 mb-4">
          <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-lg font-medium">Save Merchant Details First</p>
          <p className="text-sm">Please save the merchant details before configuring WeChat Pay settings.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">WeChat Pay Settings</h3>
        <p className="text-sm text-gray-600 mb-6">
          Configure WeChat Pay payment processing settings for this merchant.
        </p>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="space-y-6">
          {/* WeChat Rate */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              WeChat Pay Rate (%)
            </label>
            <div className="relative">
              <input
                type="number"
                step="0.01"
                min="0"
                max="100"
                value={formData.wechat_rate}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  wechat_rate: e.target.value ? parseFloat(e.target.value) : 0 
                }))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12"
                placeholder="0.00"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <span className="text-gray-500 text-sm">%</span>
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              The percentage rate charged for WeChat Pay transactions
            </p>
          </div>

          {/* Active Status */}
          <div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="wechat-active"
                checked={formData.active}
                onChange={(e) => setFormData(prev => ({ ...prev, active: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="wechat-active" className="ml-2 block text-sm text-gray-900">
                Enable WeChat Pay for this merchant
              </label>
            </div>
            <p className="text-xs text-gray-500 mt-1 ml-6">
              When enabled, this merchant can accept WeChat Pay payments
            </p>
          </div>

          {/* Current Settings Display */}
          {merchantWechat && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Current Settings</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">WeChat Rate:</span>
                  <span className="font-medium">{merchantWechat.wechat_rate || 0}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className={`font-medium ${merchantWechat.active ? 'text-green-600' : 'text-red-600'}`}>
                    {merchantWechat.active ? 'Active' : 'Inactive'}
                  </span>
                </div>
                {merchantWechat.create_dt && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Created:</span>
                    <span className="font-medium">
                      {new Date(merchantWechat.create_dt).toLocaleDateString()}
                    </span>
                  </div>
                )}
                {merchantWechat.update_dt && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Last Updated:</span>
                    <span className="font-medium">
                      {new Date(merchantWechat.update_dt).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Save Button */}
          <div className="flex justify-end pt-4 border-t border-gray-200">
            <Button
              onClick={handleSave}
              variant="primary"
              disabled={isSaving}
              className="min-w-[120px]"
            >
              {isSaving ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Saving...
                </div>
              ) : (
                merchantWechat ? "Update Settings" : "Save Settings"
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Information Panel */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              WeChat Pay Information
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>WeChat Pay rate is the percentage fee charged for each transaction</li>
                <li>Rate should be between 0% and 100%</li>
                <li>Disabling WeChat Pay will prevent this merchant from accepting WeChat payments</li>
                <li>Changes take effect immediately after saving</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
