

interface Merchant {
  merchant_id?: number;
  merchant_name: string;
  merchant_vat?: string;
  merchant_type: 'main' | 'sub';
  merchant_sub_name?: string;
  merchant_mcc?: string;
  merchant_id_wechat?: string;
  phone?: string;
  email?: string;
  address?: string;
  zipcode?: string;
  remark?: string;
  invoice_name?: string;
  invoice_tax?: string;
  invoice_address?: string;
  contact_person?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_fax?: string;
  group_id?: number;
  zone_id?: number;
  product_id?: number;
  category_id?: number;
  rate_min_transfer?: number;
  transfer_fee?: number;
  withholding_tax?: number;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface MerchantDetailsTabProps {
  formData: Omit<Merchant, "merchant_id" | "create_dt" | "update_dt">;
  onInputChange: (field: keyof Merchant, value: any) => void;
  editingMerchant: Merchant | null;
}

export function MerchantDetailsTab({ formData, onInputChange }: MerchantDetailsTabProps) {
  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Merchant Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.merchant_name}
              onChange={(e) => onInputChange("merchant_name", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter merchant name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Merchant Type <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.merchant_type}
              onChange={(e) => onInputChange("merchant_type", e.target.value as 'main' | 'sub')}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="main">Main Merchant</option>
              <option value="sub">Sub Merchant</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              VAT Number
            </label>
            <input
              type="text"
              value={formData.merchant_vat || ""}
              onChange={(e) => onInputChange("merchant_vat", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter VAT number"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Sub Merchant Name
            </label>
            <input
              type="text"
              value={formData.merchant_sub_name || ""}
              onChange={(e) => onInputChange("merchant_sub_name", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter sub merchant name"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              MCC Code
            </label>
            <input
              type="text"
              value={formData.merchant_mcc || ""}
              onChange={(e) => onInputChange("merchant_mcc", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter MCC code"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              WeChat Merchant ID
            </label>
            <input
              type="text"
              value={formData.merchant_id_wechat || ""}
              onChange={(e) => onInputChange("merchant_id_wechat", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter WeChat merchant ID"
            />
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Phone
            </label>
            <input
              type="tel"
              value={formData.phone || ""}
              onChange={(e) => onInputChange("phone", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter phone number"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Email
            </label>
            <input
              type="email"
              value={formData.email || ""}
              onChange={(e) => onInputChange("email", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter email address"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Address
            </label>
            <textarea
              value={formData.address || ""}
              onChange={(e) => onInputChange("address", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter address"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Zip Code
            </label>
            <input
              type="text"
              value={formData.zipcode || ""}
              onChange={(e) => onInputChange("zipcode", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter zip code"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Person
            </label>
            <input
              type="text"
              value={formData.contact_person || ""}
              onChange={(e) => onInputChange("contact_person", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter contact person name"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Email
            </label>
            <input
              type="email"
              value={formData.contact_email || ""}
              onChange={(e) => onInputChange("contact_email", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter contact email"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Phone
            </label>
            <input
              type="tel"
              value={formData.contact_phone || ""}
              onChange={(e) => onInputChange("contact_phone", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter contact phone"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Fax
            </label>
            <input
              type="tel"
              value={formData.contact_fax || ""}
              onChange={(e) => onInputChange("contact_fax", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter contact fax"
            />
          </div>
        </div>
      </div>

      {/* Invoice Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Invoice Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Invoice Name
            </label>
            <input
              type="text"
              value={formData.invoice_name || ""}
              onChange={(e) => onInputChange("invoice_name", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter invoice name"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Invoice Tax ID
            </label>
            <input
              type="text"
              value={formData.invoice_tax || ""}
              onChange={(e) => onInputChange("invoice_tax", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter invoice tax ID"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Invoice Address
            </label>
            <textarea
              value={formData.invoice_address || ""}
              onChange={(e) => onInputChange("invoice_address", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter invoice address"
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* Financial Settings */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Financial Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Min Transfer Rate
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.rate_min_transfer || ""}
              onChange={(e) => onInputChange("rate_min_transfer", e.target.value ? parseFloat(e.target.value) : undefined)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="0.00"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Transfer Fee
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.transfer_fee || ""}
              onChange={(e) => onInputChange("transfer_fee", e.target.value ? parseFloat(e.target.value) : undefined)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="0.00"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Withholding Tax (%)
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.withholding_tax || ""}
              onChange={(e) => onInputChange("withholding_tax", e.target.value ? parseFloat(e.target.value) : undefined)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="0.00"
            />
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Remarks
            </label>
            <textarea
              value={formData.remark || ""}
              onChange={(e) => onInputChange("remark", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter any additional remarks"
              rows={3}
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="active"
              checked={formData.active}
              onChange={(e) => onInputChange("active", e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="active" className="ml-2 block text-sm text-gray-900">
              Active
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}
