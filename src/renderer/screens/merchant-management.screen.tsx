import React, { useState, useEffect, useCallback } from "react";
import { safeIpcInvoke } from "../utils/electron";
import { Button } from "../components/button";
import { useAuth } from "../contexts/AuthContext";
import { useNotification } from "../contexts/NotificationContext";
import { MerchantModal } from "../components/merchant/MerchantModal";

interface Merchant {
  merchant_id?: number;
  merchant_name: string;
  merchant_vat?: string;
  merchant_type: 'main' | 'sub';
  merchant_sub_name?: string;
  merchant_mcc?: string;
  merchant_id_wechat?: string;
  phone?: string;
  email?: string;
  address?: string;
  zipcode?: string;
  remark?: string;
  invoice_name?: string;
  invoice_tax?: string;
  invoice_address?: string;
  contact_person?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_fax?: string;
  group_id?: number;
  zone_id?: number;
  product_id?: number;
  category_id?: number;
  rate_min_transfer?: number;
  transfer_fee?: number;
  withholding_tax?: number;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface MerchantBank {
  merchant_bank_id?: number;
  merchant_id: number;
  bank_id: number;
  bank_code?: string;
  bank_name_th?: string;
  bank_name_en?: string;
  bank_account_no: string;
  bank_account_name?: string;
  bank_branch_name?: string;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface MerchantWechat {
  merchant_wechat_id?: number;
  merchant_id: number;
  wechat_rate?: number;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface MerchantUnionpay {
  merchant_unionpay_id?: number;
  merchant_id: number;
  mdr_local?: number;
  mdr_normal?: number;
  mdr_premium?: number;
  mdr_diamond?: number;
  mdr_qrcode?: number;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface Bank {
  bank_id: number;
  bank_code: string;
  bank_name_th?: string;
  bank_name_en?: string;
  active: boolean;
}

interface MerchantResponse {
  success: boolean;
  message: string;
  data?: Merchant | Merchant[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}

export function MerchantManagementScreen() {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  
  // State for merchants list
  const [merchants, setMerchants] = useState<Merchant[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(() => {
    const saved = localStorage.getItem('merchantManagement_pageSize');
    return saved ? parseInt(saved) : 10;
  });
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [hasPreviousPage, setHasPreviousPage] = useState(false);
  
  // Search state
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  
  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingMerchant, setEditingMerchant] = useState<Merchant | null>(null);
  
  // Form data state
  const [formData, setFormData] = useState<Omit<Merchant, "merchant_id" | "create_dt" | "update_dt">>({
    merchant_name: "",
    merchant_type: "main",
    active: true,
    create_by: user?.user_name || "SYSTEM",
  });
  
  // Banks state for dropdown
  const [banks, setBanks] = useState<Bank[]>([]);
  
  // Related data state
  const [merchantBanks, setMerchantBanks] = useState<MerchantBank[]>([]);
  const [merchantWechat, setMerchantWechat] = useState<MerchantWechat | null>(null);
  const [merchantUnionpay, setMerchantUnionpay] = useState<MerchantUnionpay | null>(null);
  
  // Load merchants with pagination and search
  const loadMerchants = useCallback(async (page: number = currentPage, search: string = debouncedSearchTerm) => {
    setIsLoading(true);
    
    try {
      const response: MerchantResponse = await safeIpcInvoke("get-merchants", {
        page,
        pageSize,
        search,
        sortBy: "merchant_id",
        sortOrder: "ASC",
      });
      
      if (response.success) {
        setMerchants(response.data as Merchant[]);
        
        if (response.pagination) {
          setCurrentPage(response.pagination.currentPage);
          setTotalPages(response.pagination.totalPages);
          setTotalRecords(response.pagination.totalRecords);
          setHasNextPage(response.pagination.hasNextPage);
          setHasPreviousPage(response.pagination.hasPreviousPage);
        }
      } else {
        showNotification({
          type: "error",
          message: response.message || "Failed to load merchants",
        });
      }
    } catch (error) {
      console.error("Error loading merchants:", error);
      showNotification({
        type: "error",
        message: "Error loading merchants",
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, debouncedSearchTerm, pageSize, showNotification]);
  
  // Load banks for dropdown
  const loadBanks = useCallback(async () => {
    try {
      const response = await safeIpcInvoke("get-banks", {
        pageSize: 100,
        sortBy: "bank_code",
      });
      
      if (response.success) {
        setBanks(response.data as Bank[]);
      } else {
        console.error("Failed to load banks:", response.message);
      }
    } catch (error) {
      console.error("Error loading banks:", error);
    }
  }, []);
  
  // Initial data loading
  useEffect(() => {
    loadMerchants();
    loadBanks();
  }, [loadMerchants, loadBanks]);
  
  // Search term debouncing
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to first page on search
    }, 500);
    
    return () => clearTimeout(timer);
  }, [searchTerm]);
  
  // Reload when debounced search term changes
  useEffect(() => {
    loadMerchants(1, debouncedSearchTerm);
  }, [debouncedSearchTerm, loadMerchants]);
  
  // Save page size preference
  useEffect(() => {
    localStorage.setItem('merchantManagement_pageSize', pageSize.toString());
  }, [pageSize]);
  
  // Handle page size change
  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = parseInt(e.target.value);
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };
  
  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadMerchants(page);
  };
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };
  
  // Handle form input changes
  const handleInputChange = (field: keyof Merchant, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };
  
  // Create new merchant
  const handleCreate = () => {
    setEditingMerchant(null);
    setFormData({
      merchant_name: "",
      merchant_type: "main",
      active: true,
      create_by: user?.user_name || "SYSTEM",
    });
    setMerchantBanks([]);
    setMerchantWechat(null);
    setMerchantUnionpay(null);
    setIsModalOpen(true);
  };
  
  // Edit existing merchant
  const handleEdit = async (merchant: Merchant) => {
    setEditingMerchant(merchant);
    setFormData({
      merchant_name: merchant.merchant_name,
      merchant_vat: merchant.merchant_vat || "",
      merchant_type: merchant.merchant_type,
      merchant_sub_name: merchant.merchant_sub_name || "",
      merchant_mcc: merchant.merchant_mcc || "",
      merchant_id_wechat: merchant.merchant_id_wechat || "",
      phone: merchant.phone || "",
      email: merchant.email || "",
      address: merchant.address || "",
      zipcode: merchant.zipcode || "",
      remark: merchant.remark || "",
      invoice_name: merchant.invoice_name || "",
      invoice_tax: merchant.invoice_tax || "",
      invoice_address: merchant.invoice_address || "",
      contact_person: merchant.contact_person || "",
      contact_email: merchant.contact_email || "",
      contact_phone: merchant.contact_phone || "",
      contact_fax: merchant.contact_fax || "",
      group_id: merchant.group_id,
      zone_id: merchant.zone_id,
      product_id: merchant.product_id,
      category_id: merchant.category_id,
      rate_min_transfer: merchant.rate_min_transfer,
      transfer_fee: merchant.transfer_fee,
      withholding_tax: merchant.withholding_tax,
      active: merchant.active,
      create_by: merchant.create_by,
      update_by: user?.user_name || "SYSTEM",
    });

    // Load related data
    if (merchant.merchant_id) {
      try {
        // Load bank accounts
        const bankResponse = await safeIpcInvoke("get-merchant-banks", merchant.merchant_id);
        if (bankResponse.success) {
          setMerchantBanks(bankResponse.data || []);
        }

        // Load WeChat settings
        const wechatResponse = await safeIpcInvoke("get-merchant-wechat", merchant.merchant_id);
        if (wechatResponse.success && wechatResponse.data && wechatResponse.data.length > 0) {
          setMerchantWechat(wechatResponse.data[0]);
        } else {
          setMerchantWechat(null);
        }

        // Load UnionPay settings
        const unionpayResponse = await safeIpcInvoke("get-merchant-unionpay", merchant.merchant_id);
        if (unionpayResponse.success && unionpayResponse.data && unionpayResponse.data.length > 0) {
          setMerchantUnionpay(unionpayResponse.data[0]);
        } else {
          setMerchantUnionpay(null);
        }
      } catch (error) {
        console.error("Error loading merchant related data:", error);
      }
    }

    setIsModalOpen(true);
  };

  // Delete merchant
  const handleDelete = async (merchant: Merchant) => {
    if (!merchant.merchant_id) return;

    if (window.confirm(`Are you sure you want to delete merchant "${merchant.merchant_name}"?`)) {
      try {
        const response = await safeIpcInvoke("delete-merchant", merchant.merchant_id);

        if (response.success) {
          showNotification({
            type: "success",
            message: "Merchant deleted successfully",
          });
          loadMerchants(); // Reload the list
        } else {
          showNotification({
            type: "error",
            message: response.message || "Failed to delete merchant",
          });
        }
      } catch (error) {
        console.error("Error deleting merchant:", error);
        showNotification({
          type: "error",
          message: "Error deleting merchant",
        });
      }
    }
  };

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      let response;

      if (editingMerchant && editingMerchant.merchant_id) {
        // Update existing merchant
        response = await safeIpcInvoke(
          "update-merchant",
          editingMerchant.merchant_id,
          formData
        );
      } else {
        // Create new merchant
        response = await safeIpcInvoke("create-merchant", formData);
      }

      if (response.success) {
        showNotification({
          type: "success",
          message: editingMerchant
            ? "Merchant updated successfully"
            : "Merchant created successfully",
        });
        setIsModalOpen(false);
        await loadMerchants(); // Reload the list
      } else {
        showNotification({
          type: "error",
          message: `Failed to ${editingMerchant ? "update" : "create"} merchant: ${
            response.message
          }`,
        });
      }
    } catch (error) {
      console.error("Error saving merchant:", error);
      showNotification({
        type: "error",
        message: "Error saving merchant",
      });
    }
  };

  // Render merchant type badge
  const renderMerchantTypeBadge = (type: string) => {
    if (type === "main") {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          Main
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          Sub
        </span>
      );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Merchant Management</h1>
              <p className="text-gray-600 mt-1">
                Manage merchant information and payment settings
              </p>
            </div>
            <Button
              onClick={handleCreate}
              variant="primary"
              size="md"
              className="inline-flex items-center gap-2"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Add New Merchant
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Merchants
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={handleSearchChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Search by name, VAT, email, or phone..."
              />
            </div>
            <div className="sm:w-48">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Items per page
              </label>
              <select
                value={pageSize}
                onChange={handlePageSizeChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value={5}>5 per page</option>
                <option value={10}>10 per page</option>
                <option value={25}>25 per page</option>
                <option value={50}>50 per page</option>
              </select>
            </div>
          </div>
        </div>

        {/* Merchants Table */}
        {isLoading ? (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading merchants...</span>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Merchant Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      VAT
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {merchants.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="px-6 py-12 text-center">
                        <div className="flex flex-col items-center justify-center">
                          <svg
                            className="w-12 h-12 text-gray-400 mb-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                            />
                          </svg>
                          <h3 className="text-lg font-medium text-gray-900 mb-2">
                            No merchants found
                          </h3>
                          <p className="text-gray-500 mb-4">
                            Get started by creating your first merchant
                          </p>
                          <Button
                            onClick={handleCreate}
                            variant="primary"
                            size="sm"
                          >
                            Add New Merchant
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    merchants.map((merchant) => (
                      <tr
                        key={merchant.merchant_id || merchant.merchant_name}
                        className="hover:bg-gray-50 transition-colors"
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          #{merchant.merchant_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {merchant.merchant_name}
                            </div>
                            {merchant.merchant_sub_name && (
                              <div className="text-sm text-gray-500">
                                {merchant.merchant_sub_name}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {renderMerchantTypeBadge(merchant.merchant_type)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {merchant.merchant_vat || "-"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {merchant.email || "-"}
                          </div>
                          <div className="text-sm text-gray-500">
                            {merchant.phone || "-"}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              merchant.active
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {merchant.active ? "Active" : "Inactive"}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            <Button
                              onClick={() => handleEdit(merchant)}
                              variant="secondary"
                              size="sm"
                              className="inline-flex items-center gap-1"
                            >
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                />
                              </svg>
                              Edit
                            </Button>
                            <Button
                              onClick={() => handleDelete(merchant)}
                              variant="danger"
                              size="sm"
                              className="inline-flex items-center gap-1"
                            >
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                              </svg>
                              Delete
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Pagination Controls */}
        {!isLoading && totalPages > 1 && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-6">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="text-sm text-gray-600">
                Showing{" "}
                <span className="font-medium">
                  {merchants.length > 0 ? (currentPage - 1) * pageSize + 1 : 0}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(currentPage * pageSize, totalRecords)}
                </span>{" "}
                of <span className="font-medium">{totalRecords}</span> merchants
              </div>

              <div className="flex items-center gap-2">
                <Button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={!hasPreviousPage}
                  variant="secondary"
                  size="sm"
                >
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        variant={currentPage === pageNum ? "primary" : "secondary"}
                        size="sm"
                        className="min-w-[2.5rem]"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={!hasNextPage}
                  variant="secondary"
                  size="sm"
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Merchant Modal */}
      <MerchantModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        editingMerchant={editingMerchant}
        formData={formData}
        setFormData={setFormData}
        merchantBanks={merchantBanks}
        setMerchantBanks={setMerchantBanks}
        merchantWechat={merchantWechat}
        setMerchantWechat={setMerchantWechat}
        merchantUnionpay={merchantUnionpay}
        setMerchantUnionpay={setMerchantUnionpay}
        banks={banks}
        onSubmit={handleSubmit}
        onRefresh={loadMerchants}
      />
    </div>
  );
}
