import React, { useState, useEffect } from "react";
import { Button } from "../components/button";
import { safeIpcInvoke } from "../utils/electron";
import { useNotification } from "../contexts/NotificationContext";

interface NetworkServiceConfig {
  id?: number;
  // International Consumer Card
  intl_consumer_dual_brand: number;
  intl_consumer_prepaid_pin: number;
  intl_consumer_credit_classic: number;
  intl_consumer_credit_platinum: number;
  intl_consumer_credit_diamond: number;

  // International Commercial Card
  intl_commercial_amount_fee: number;
  intl_commercial_amount_min: number;
  intl_commercial_te_merchants: number;

  // Thai Payment Network - POS
  thai_pos_government: number;
  thai_pos_non_government: number;
  thai_pos_non_government_max: number;

  // Thai Payment Network - E-Commerce
  thai_ecommerce_government: number;
  thai_ecommerce_non_government: number;
  thai_ecommerce_non_government_max: number;

  // Tax Configuration
  vat_percentage: number;
  withhold_tax_percentage: number;

  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

export function NetworkServiceScreen() {
  const [config, setConfig] = useState<NetworkServiceConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const { showNotification } = useNotification();
  const [formData, setFormData] = useState<
    Omit<NetworkServiceConfig, "id" | "create_dt" | "update_dt">
  >({
    // International Consumer Card
    intl_consumer_dual_brand: 1.2,
    intl_consumer_prepaid_pin: 1.15,
    intl_consumer_credit_classic: 1.2,
    intl_consumer_credit_platinum: 1.95,
    intl_consumer_credit_diamond: 2.1,

    // International Commercial Card
    intl_commercial_amount_fee: 0.75,
    intl_commercial_amount_min: 25.0,
    intl_commercial_te_merchants: 2.0,

    // Thai Payment Network - POS
    thai_pos_government: 0.0,
    thai_pos_non_government: 0.3,
    thai_pos_non_government_max: 0.3,

    // Thai Payment Network - E-Commerce
    thai_ecommerce_government: 0.0,
    thai_ecommerce_non_government: 0.6,
    thai_ecommerce_non_government_max: 0.6,

    // Tax Configuration
    vat_percentage: 7.0,
    withhold_tax_percentage: 3.0,

    active: true,
    create_by: "SYSTEM",
  });

  // Load network service configuration from database
  const loadNetworkServiceConfig = async () => {
    try {
      setIsInitializing(true);

      // Initialize table first
      await safeIpcInvoke("init-network-service-table");

      // Load existing configuration
      const response = await safeIpcInvoke("get-network-service-config");

      if (response.success && response.data) {
        setConfig(response.data);
        setFormData({
          intl_consumer_dual_brand: response.data.intl_consumer_dual_brand,
          intl_consumer_prepaid_pin: response.data.intl_consumer_prepaid_pin,
          intl_consumer_credit_classic:
            response.data.intl_consumer_credit_classic,
          intl_consumer_credit_platinum:
            response.data.intl_consumer_credit_platinum,
          intl_consumer_credit_diamond:
            response.data.intl_consumer_credit_diamond,
          intl_commercial_amount_fee: response.data.intl_commercial_amount_fee,
          intl_commercial_amount_min: response.data.intl_commercial_amount_min,
          intl_commercial_te_merchants:
            response.data.intl_commercial_te_merchants,
          thai_pos_government: response.data.thai_pos_government,
          thai_pos_non_government: response.data.thai_pos_non_government,
          thai_pos_non_government_max:
            response.data.thai_pos_non_government_max,
          thai_ecommerce_government: response.data.thai_ecommerce_government,
          thai_ecommerce_non_government:
            response.data.thai_ecommerce_non_government,
          thai_ecommerce_non_government_max:
            response.data.thai_ecommerce_non_government_max,
          vat_percentage: response.data.vat_percentage,
          withhold_tax_percentage: response.data.withhold_tax_percentage,
          active: response.data.active,
          create_by: response.data.create_by,
        });
      } else {
        // No configuration found, use default values
        const defaultConfig = {
          intl_consumer_dual_brand: 1.2,
          intl_consumer_prepaid_pin: 1.15,
          intl_consumer_credit_classic: 1.2,
          intl_consumer_credit_platinum: 1.95,
          intl_consumer_credit_diamond: 2.1,
          intl_commercial_amount_fee: 0.75,
          intl_commercial_amount_min: 25.0,
          intl_commercial_te_merchants: 2.0,
          thai_pos_government: 0.0,
          thai_pos_non_government: 0.3,
          thai_pos_non_government_max: 0.3,
          thai_ecommerce_government: 0.0,
          thai_ecommerce_non_government: 0.6,
          thai_ecommerce_non_government_max: 0.6,
          vat_percentage: 7.0,
          withhold_tax_percentage: 3.0,
          active: true,
          create_by: "SYSTEM",
        };

        setFormData(defaultConfig);
      }
    } catch (error: any) {
      console.error("Error loading network service configuration:", error);
      showNotification(
        "Error loading network service configuration: " + error.message,
        "error"
      );
    } finally {
      setIsInitializing(false);
    }
  };

  useEffect(() => {
    loadNetworkServiceConfig();
  }, []);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    if (config) {
      setFormData({
        intl_consumer_dual_brand: config.intl_consumer_dual_brand,
        intl_consumer_prepaid_pin: config.intl_consumer_prepaid_pin,
        intl_consumer_credit_classic: config.intl_consumer_credit_classic,
        intl_consumer_credit_platinum: config.intl_consumer_credit_platinum,
        intl_consumer_credit_diamond: config.intl_consumer_credit_diamond,
        intl_commercial_amount_fee: config.intl_commercial_amount_fee,
        intl_commercial_amount_min: config.intl_commercial_amount_min,
        intl_commercial_te_merchants: config.intl_commercial_te_merchants,
        thai_pos_government: config.thai_pos_government,
        thai_pos_non_government: config.thai_pos_non_government,
        thai_pos_non_government_max: config.thai_pos_non_government_max,
        thai_ecommerce_government: config.thai_ecommerce_government,
        thai_ecommerce_non_government: config.thai_ecommerce_non_government,
        thai_ecommerce_non_government_max:
          config.thai_ecommerce_non_government_max,
        vat_percentage: config.vat_percentage,
        withhold_tax_percentage: config.withhold_tax_percentage,
        active: config.active,
        create_by: config.create_by,
      });
    }
    setIsEditing(false);
  };

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Save to database
      const response = await safeIpcInvoke("save-network-service-config", {
        ...formData,
        update_by: "SYSTEM",
      });

      if (response.success && response.data) {
        setConfig(response.data);
        setIsEditing(false);
        showNotification(
          "Network service configuration saved successfully",
          "success"
        );
      } else {
        throw new Error(
          response.error || "Failed to save network service configuration"
        );
      }
    } catch (error: any) {
      console.error("Error saving configuration:", error);
      showNotification("Error saving configuration: " + error.message, "error");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get field value safely
  const getFieldValue = (field: keyof NetworkServiceConfig): number => {
    if (isEditing) {
      const value = formData[field as keyof typeof formData];
      return Number(value || 0);
    }
    const value = config?.[field] || formData[field as keyof typeof formData];
    return Number(value || 0);
  };

  if (isInitializing) {
    return (
      <div className="min-h-screen bg-gray-50 p-4 sm:p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">
            Loading network service configuration...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Network Service Fee Configuration
              </h1>
              <p className="text-gray-600 mt-1">
                Configure network service fees and transaction charges
              </p>
            </div>
            <div className="flex gap-2">
              {!isEditing ? (
                <Button
                  onClick={handleEdit}
                  variant="primary"
                  size="md"
                  className="inline-flex items-center gap-2"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                  Edit Configuration
                </Button>
              ) : (
                <>
                  <Button
                    onClick={handleCancel}
                    variant="secondary"
                    size="md"
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    variant="primary"
                    size="md"
                    disabled={isLoading}
                    className="inline-flex items-center gap-2"
                  >
                    {isLoading ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    )}
                    Save Changes
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Configuration Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* International Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                International (Purchase, pre-authorization in non-E-commerce
                channel and corresponding refund transactions)
              </h2>
            </div>

            <div className="p-6 space-y-6">
              {/* Consumer Card Section */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Consumer Card
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Dual Brand Indicator=0
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        step="0.001"
                        value={getFieldValue("intl_consumer_dual_brand")}
                        onChange={(e) =>
                          handleInputChange(
                            "intl_consumer_dual_brand",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        %
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Prepaid Card and PIN-based Debit Card (D,P)
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        step="0.001"
                        value={getFieldValue("intl_consumer_prepaid_pin")}
                        onChange={(e) =>
                          handleInputChange(
                            "intl_consumer_prepaid_pin",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        %
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Credit Card (C) - Classic_Gold
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        step="0.001"
                        value={getFieldValue("intl_consumer_credit_classic")}
                        onChange={(e) =>
                          handleInputChange(
                            "intl_consumer_credit_classic",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        %
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Credit Card (C) - Platinum
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        step="0.001"
                        value={getFieldValue("intl_consumer_credit_platinum")}
                        onChange={(e) =>
                          handleInputChange(
                            "intl_consumer_credit_platinum",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        %
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Credit Card (C) - Diamond and above
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        step="0.001"
                        value={getFieldValue("intl_consumer_credit_diamond")}
                        onChange={(e) =>
                          handleInputChange(
                            "intl_consumer_credit_diamond",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        %
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Commercial Card Section */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Commercial Card
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Amount Per Transactions {">"} 2,000 US and Non-T/E MCCs
                    </label>
                    <div className="flex flex-row gap-2 items-center">
                      <div className="relative">
                        <input
                          type="number"
                          step="0.001"
                          value={getFieldValue("intl_commercial_amount_fee")}
                          onChange={(e) =>
                            handleInputChange(
                              "intl_commercial_amount_fee",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          disabled={!isEditing}
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          %
                        </span>
                      </div>
                      <span>+</span>
                      <div className="relative">
                        <input
                          type="number"
                          step="0.001"
                          value={getFieldValue("intl_commercial_amount_min")}
                          onChange={(e) =>
                            handleInputChange(
                              "intl_commercial_amount_min",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          disabled={!isEditing}
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          US
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Amount (US)
                    </label>
                    <input
                      type="number"
                      step="0.001"
                      value={getFieldValue("intl_commercial_amount_min")}
                      onChange={(e) => handleInputChange("intl_commercial_amount_min", parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                    />
                  </div> */}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      T_E Merchants
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        step="0.001"
                        value={getFieldValue("intl_commercial_te_merchants")}
                        onChange={(e) =>
                          handleInputChange(
                            "intl_commercial_te_merchants",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        %
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Thai Payment Network Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                THAI PAYMENT NETWORK FEE
              </h2>
            </div>

            <div className="p-6 space-y-6">
              {/* POS Section */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  POS
                </h3>
                <div className="grid grid-cols-1 gap-4">
                  <div className="grid grid-cols-1 md:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                      Merchant Category: Government (Baht Per Transaction)
                    </label>
                    <input
                      type="number"
                      step="0.001"
                      value={getFieldValue("thai_pos_government")}
                      onChange={(e) =>
                        handleInputChange(
                          "thai_pos_government",
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                    />
                    </div>
                    
                  </div>

                  <div className="flex flex-row gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Merchant Category: Non-Government
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          step="0.001"
                          value={getFieldValue("thai_pos_non_government")}
                          onChange={(e) =>
                            handleInputChange(
                              "thai_pos_non_government",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          disabled={!isEditing}
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          %
                        </span>
                      </div>
                    </div>
                     <span className="self-end">
                      {"<="}
                    </span>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Non-Government Max (Baht)
                      </label>
                      <input
                        type="number"
                        step="0.001"
                        value={getFieldValue("thai_pos_non_government_max")}
                        onChange={(e) =>
                          handleInputChange(
                            "thai_pos_non_government_max",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                    </div>
                  </div>
                </div>
                <div>
                  <p className="text-xs italic text-gray-500 mt-1">
                    Note: กรณีที่ไม่ใช่หน่วยงานราชการคิดที่ 0.25%
                    แต่คำนวณแล้วต้องไม่มากกว่า 0.29 บาทต่อรายการ
                  </p>
                </div>
              </div>

              {/* E-Commerce Section */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  E-COMMERCE
                </h3>
                <div className="grid grid-cols-1 gap-4">
                  <div className="grid grid-cols-1 md:grid-cols-2">
                        <div>
                           <label className="block text-sm font-medium text-gray-700 mb-2">
                      Merchant Category: Government (Baht Per Transaction)
                    </label>
                    <input
                      type="number"
                      step="0.001"
                      value={getFieldValue("thai_ecommerce_government")}
                      onChange={(e) =>
                        handleInputChange(
                          "thai_ecommerce_government",
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                    />
                        </div>
                  </div>
                  <div className="flex flex-row gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Merchant Category: Non-Government
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          step="0.001"
                          value={getFieldValue("thai_ecommerce_non_government")}
                          onChange={(e) =>
                            handleInputChange(
                              "thai_ecommerce_non_government",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          disabled={!isEditing}
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          %
                        </span>
                      </div>
                    </div>
                    <span className="self-end">
                      {"<="}
                    </span>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Non-Government Max (Baht)
                      </label>
                      <input
                        type="number"
                        step="0.001"
                        value={getFieldValue(
                          "thai_ecommerce_non_government_max"
                        )}
                        onChange={(e) =>
                          handleInputChange(
                            "thai_ecommerce_non_government_max",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={!isEditing}
                      />
                    </div>
                  </div>
                </div>
                <div>
                  <p className="text-xs italic text-gray-500 mt-1">
                    Note: กรณีที่ไม่ใช่หน่วยงานราชการคิดที่ 0.25%
                    แต่คำนวณแล้วต้องไม่มากกว่า 0.29 บาทต่อรายการ
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Tax Configuration Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                Tax Configuration
              </h2>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    VAT Percentage
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      step="0.01"
                      value={getFieldValue("vat_percentage")}
                      onChange={(e) =>
                        handleInputChange(
                          "vat_percentage",
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                      placeholder="7.00"
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                      %
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Value Added Tax percentage applied to transactions
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Withhold Tax Percentage
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      step="0.01"
                      value={getFieldValue("withhold_tax_percentage")}
                      onChange={(e) =>
                        handleInputChange(
                          "withhold_tax_percentage",
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!isEditing}
                      placeholder="3.00"
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                      %
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Withholding tax percentage deducted from payments
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Status Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    Configuration Status
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Last updated:{" "}
                    {config?.update_dt
                      ? new Date(config.update_dt).toLocaleString()
                      : config?.create_dt
                      ? new Date(config.create_dt).toLocaleString()
                      : "Not available"}
                  </p>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="configActive"
                    checked={
                      isEditing ? formData.active : config?.active ?? true
                    }
                    onChange={(e) =>
                      handleInputChange("active", e.target.checked)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={!isEditing}
                  />
                  <label
                    htmlFor="configActive"
                    className="ml-2 block text-sm text-gray-700"
                  >
                    Configuration Active
                  </label>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
