import { NavLink, Outlet, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight, Menu } from "lucide-react"; // Import icons from lucide-react

import { Separator } from "./components/ui/separator";
import { useAuth, useIsAdmin } from "./contexts/AuthContext";
import { Button } from "./components/button";
import { NotificationContainer } from "./components/NotificationContainer";
import logoImage from "../assets/E-POS-logo-1.png";

export function Layout() {
  const { user, logout } = useAuth();
  const isAdmin = useIsAdmin();
  const location = useLocation();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Function to get page title based on current route
  const getPageTitle = (pathname: string): string => {
    const routeTitles: Record<string, string> = {
      '/': 'Dashboard',
      '/about': 'About',
      '/example-usage': 'PDF Example',
      '/csv-upload': 'CSV Upload',
      '/csv-folder-monitor': 'CSV Monitor',
      '/postgresql-test': 'PostgreSQL Test',
      '/pcloud-test': 'pCloud Test',
      '/bank-master': 'Bank Master',
      '/network-service': 'Network Service',
      '/company-settings': 'Company Settings',
      '/user-management': 'User Management',
      '/modal-examples': 'Modal Examples',
      '/todos': 'Todos'
    };
    
    return routeTitles[pathname] || 'Dashboard';
  };

  // Check if screen is mobile size
  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768; // md breakpoint
      setIsMobile(mobile);
      if (mobile) {
        setSidebarCollapsed(true);
        setSidebarOpen(false);
      }
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const navigationItems = [
    { to: "/", label: "Main", icon: "🏠" },
    { to: "/about", label: "About", icon: "ℹ️" },
    { to: "/example-usage", label: "PDF Example", icon: "📄" },
    { to: "/csv-upload", label: "CSV Upload", icon: "📤" },
    { to: "/csv-folder-monitor", label: "CSV Monitor", icon: "👁️" },
    { to: "/postgresql-test", label: "PostgreSQL Test", icon: "🗄️" },
    { to: "/pcloud-test", label: "pCloud Test", icon: "☁️" },
  ];

  const masterItems = [
    { to: "/bank-master", label: "Bank Master", icon: "🏦" },
    { to: "/network-service", label: "Network Service", icon: "🌐" },
    { to: "/company-settings", label: "Company Settings", icon: "🏢" },
  ];

  const adminItems = [
    { to: "/user-management", label: "User Management", icon: "👥" },
  ];

  const toggleSidebar = () => {
    if (isMobile) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Mobile Overlay */}
      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`bg-white shadow-lg transition-all duration-300 flex flex-col z-50 ${
          isMobile
            ? `fixed left-0 top-0 h-full ${
                sidebarOpen ? "w-64 translate-x-0" : "w-64 -translate-x-full"
              } md:relative md:translate-x-0`
            : sidebarCollapsed
            ? "w-20"
            : "w-64"
        }`}
      >
        {/* Sidebar Header */}
        <div className="p-4">
            <div
            className={`flex items-center ${
              sidebarCollapsed || isMobile ? "justify-center" : "justify-between"
            }`}
            >
            {(!sidebarCollapsed || isMobile) && (
              <img 
                src={logoImage} 
                alt="Epos Service" 
                className="h-8 w-auto"
              />
            )}
            <button
              onClick={toggleSidebar}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              {isMobile ? (
                <Menu size={24} />
              ) : sidebarCollapsed ? (
                <ChevronRight size={24} />
              ) : (
                <ChevronLeft size={24} />
              )}
            </button>
          </div>
        </div>

        {/* Navigation Items */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {navigationItems.map((item) => (
            <NavLink
              key={item.to}
              to={item.to}
              onClick={() => isMobile && setSidebarOpen(false)}
              className={({ isActive }) =>
                `flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${
                  isActive
                    ? isMobile
                      ? "bg-blue-200 text-blue-800 "
                      : sidebarCollapsed
                      ? "bg-blue-50 text-blue-600 justify-center"
                      : "bg-blue-100 text-blue-700"
                    : isMobile
                    ? "text-gray-800 hover:bg-gray-200"
                    : sidebarCollapsed
                    ? "text-gray-600 hover:bg-gray-50 justify-center"
                    : "text-gray-700 hover:bg-gray-100"
                }`
              }
            >
              <span className="text-lg">{item.icon}</span>
              {(!sidebarCollapsed || isMobile) && (
                <span className="font-medium">{item.label}</span>
              )}
            </NavLink>
          ))}

          <>
            <Separator className="my-4" />
            <div className="space-y-2">
              {(!sidebarCollapsed || isMobile) && (
                <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Master Data
                </h3>
              )}
              {masterItems.map((item) => (
                <NavLink
                  key={item.to}
                  to={item.to}
                  onClick={() => isMobile && setSidebarOpen(false)}
                  className={({ isActive }) =>
                    `flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${
                      isActive
                        ? isMobile
                          ? "bg-blue-200 text-blue-800 "
                          : sidebarCollapsed
                          ? "bg-blue-100 text-blue-700 justify-center"
                          : "bg-blue-100 text-blue-700 "
                        : isMobile
                        ? "text-gray-800 hover:bg-gray-200"
                        : sidebarCollapsed
                        ? "text-blue-700 hover:bg-blue-50 justify-center"
                        : "text-gray-700 hover:bg-gray-100"
                    }`
                  }
                >
                  <span className="text-lg">{item.icon}</span>
                  {(!sidebarCollapsed || isMobile) && (
                    <span className="font-medium">{item.label}</span>
                  )}
                </NavLink>
              ))}
            </div>
          </>

          {isAdmin && (
            <>
              <Separator className="my-4" />
              <div className="space-y-2">
                {(!sidebarCollapsed || isMobile) && (
                  <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Admin
                  </h3>
                )}
                {adminItems.map((item) => (
                  <NavLink
                    key={item.to}
                    to={item.to}
                    onClick={() => isMobile && setSidebarOpen(false)}
                    className={({ isActive }) =>
                      `flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${
                        isActive
                          ? isMobile
                            ? "bg-blue-200 text-blue-800 "
                            : sidebarCollapsed
                            ? "bg-blue-100 text-blue-700 justify-center"
                            : "bg-blue-100 text-blue-700 "
                          : isMobile
                          ? "text-gray-800 hover:bg-gray-200"
                          : sidebarCollapsed
                          ? "text-blue-700 hover:bg-blue-50 justify-center"
                          : "text-gray-700 hover:bg-gray-100"
                      }`
                    }
                  >
                    <span className="text-lg">{item.icon}</span>
                    {(!sidebarCollapsed || isMobile) && (
                      <span className="font-medium">{item.label}</span>
                    )}
                  </NavLink>
                ))}
              </div>
            </>
          )}
        </nav>
      </aside>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Top Navbar */}
        <header className="bg-white shadow-sm border-b border-gray-200 px-4 md:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {isMobile && (
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="p-2 rounded-lg hover:bg-gray-100 transition-colors md:hidden"
                >
                  <Menu size={24} />
                </button>
              )}
              <h2 className="text-lg font-semibold text-gray-800">{getPageTitle(location.pathname)}</h2>
            </div>

            {/* User Info and Logout */}
            <div className="flex items-center gap-2 md:gap-4">
              <div className="text-sm text-gray-600 hidden sm:block">
                <span className="font-medium">{user?.user_name}</span>
                {user?.role_name && (
                  <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                    {user.role_name}
                  </span>
                )}
              </div>

              <Button
                variant="secondary"
                size="sm"
                onClick={handleLogout}
                disabled={isLoggingOut}
                className="text-sm"
              >
                {isLoggingOut ? "Logging out..." : "Logout"}
              </Button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            <Outlet />
          </div>
        </main>
      </div>

      {/* Global Notification Container */}
      <NotificationContainer />
    </div>
  );
}
